micronaut:
  application:
    name: trading

  server:
    port: 8008

  executors:
    scheduled:
      type: scheduled
      core-pool-size: 16

logging:
  response: false

arbpath: /tmp/tmp-arb

redis:
  uri: redis://localhost

# You can override booker port to connect to using the configuration below.
#booker:
#  client:
#    port: 7755

# Each exchange top property has an implicit property `booker=true`. When
# property set as `false` bot bypasses booker and reach an exchange directly.
# Look at trading/trading/holders/OrderBookHolder.kt:167
bitmex:
  api:
    version: v1
  keys:
    socket:
      id:
      secret:
    rest:
      id:
      secret:
  host: testnet.bitmex.com
  socket:
    host: wss://ws.testnet.bitmex.com
    subscribers: 1
    proxy: false
  proxy: false

deribit:
  keys:
    all:
      id:
      secret:
  rest:
    address: https://test.deribit.com
  socket:
    address: wss://test.deribit.com
    subscribers: 2
    proxy: false
  proxy: false
  wsapi: true

huobi:
  keys:
    all:
      id:
      secret:
  host: api.hbdm.vn

okex:
  keys:
    v5:
      id:
      secret:
      passphrase:
  socket:
    address: wss://wspap.okx.com:8443/ws/v5
    subscribers: 1
    proxy: false
  address: https://www.okx.com
  proxyaddress: https://coloapi2.okx.com
  proxy: false
  wsapi: false

facilities:
  keys:
    v3:
      id:
      secret:
  host: www.cryptofacilities.com
  constants:
    XBT:
      step: 0.5
      min: 0
    ETH:
      step: 0.05
      min: 0

binance:
  keys:
    v1:
#      id: 6bj51CUgsZN4VCeJLxXEjZT89pMQ6UGHUesaQ7kv8F7XiRQLBKBJxLk5lminAmvX
#      secret: WtzUCE9RY2GYKpsWMgVFI6JY7LGCKR9HT5Pk7kXHYAJQ6o5jtDlWy4QaV2Lbb4iD
      id:
      secret:
  host: https://testnet.binancefuture.com # https://fapi.binance.com/fapi/v1/exchangeInfo
  proxy: false # usdt rest proxy
  socket:
    subscribers: 1
    host: wss://stream.binancefuture.com # fstream.binance.com
    proxy: false # usdt socket proxy
    api:
      host: wss://testnet.binancefuture.com # wss://ws-fapi.binance.com/ws-fapi/v1
      proxy: false
  coin:
    host: https://testnet.binancefuture.com # https://dapi.binance.com/dapi/v1/exchangeInfo
    proxy: false # coin rest proxy
    socket:
      subscribers: 1
      host: wss://dstream.binancefuture.com # dstream.binance.com
      proxy: false # coin socket proxy
  wsapi: false

bybit:
  keys:
    v5:
#      id: ATsSMoCfFo3v5GPdp4
#      secret: 1Sg4eRfMCNlMvtB0AUfoO13IoyYljcv9XnoJ
      id:
      secret:
  host: api.bytick.com
  socket:
    host: wss://stream-testnet.bybit.com
    subscribers: 1
    proxy: false
  proxy: false
  wsapi: false

hyperliquid:
  wallet: "******************************************"
  key: "0xd1518479e6c4fd3b728a288dd62dbc08aae7ade0c8966ed4a2f8dfcb1206a605" # testnet
#  key: "0xd44d35b93cc0c8accc20dd5b6b62208eea7a1815e58c18452a19e134d83c8063" # mainnet
#  address: https://api2.hyperliquid.xyz # mainnet
  address: https://api.hyperliquid-testnet.xyz # testnet
  socket:
#    host: wss://api2.hyperliquid.xyz # mainnet
    host: wss://api.hyperliquid-testnet.xyz # testnet
    subscribers: 1
    proxy: false
  wsapi: false # If true then turns on making and cancelling orders via websocket
  node:
    trades: true
    host: ws://***********:8822
  maintenance: https://hyperliquid.statuspage.io

bitget:
  keys:
#    id: bg_d778b7b8021be4a18c9932e1dcf593f4
#    secret: 7e762bdcdb4f37b3080fd221e7ef229bb97fcbd131b013ef2e782b9a7ddeebdc
#    passphrase: RBNko7FTXZots3hWiYG9fcKzXtR8cLiZ
    id:
    secret:
    passphrase:
  address: https://api.bitget.com
  socket:
    address: wss://ws.bitget.com/v2/ws
    subscribers: 2

gateio:
  keys:
    id:
    secret:
  address: https://api.gateio.ws
  socket:
    address: wss://fx-ws.gateio.ws/v4/ws/usdt
    subscribers: 2

telegram:
  key: **********************************************
  id: 204783
