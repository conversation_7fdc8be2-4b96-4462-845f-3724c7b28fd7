package trading.telegram.command.commands

import jakarta.inject.Singleton
import trading.storage.repos.StrategyRepository
import trading.telegram.TelegramSender
import trading.telegram.command.engine.Command

@Singleton
class KoefsCommand(
    private val strategyRepository: StrategyRepository,
    private val telegramSender: TelegramSender
) : Command() {
    override val name = "koefs"
    override val desc = "Show koefs"

    override suspend fun handle(
        args: Map<String, String>,
        flags: Set<String>
    ) {
        strategyRepository.findAll().map { str ->
            if (str.legs.leg1.koef.compareTo(1.0) != 0 || str.legs.leg2.koef.compareTo(1.0) != 0) {
                "\n${str.legs.leg1.symbolEx.symbol}: ${str.legs.leg1.koef} vs ${str.legs.leg2.symbolEx.symbol}: ${str.legs.leg2.koef}"
            } else {
                ""
            }
        }.send(telegramSender)
    }
}