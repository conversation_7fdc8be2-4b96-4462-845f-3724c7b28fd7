package trading.exchanges

import io.micronaut.context.env.Environment
import jakarta.inject.Singleton
import trading.models.SymbolEx
import java.util.*
import java.util.concurrent.ConcurrentHashMap

@Singleton
class ConstantsResolver(private val environment: Environment) {

    private val constants = ConcurrentHashMap<SymbolEx, ExchangeConstants>()

    fun getFor(symbolEx: SymbolEx): ExchangeConstants {
        return constants.computeIfAbsent(symbolEx) {
            val short = it.symbol
                .removePrefix("PI_") // facilities
                .substring(0, 3)
                .lowercase(Locale.getDefault())
            val map = environment.getProperty("${it.exchange}.constants.$short", Map::class.java).get() as Map<String, Any>
            ExchangeConstants(
                    map["step"]?.toString()?.toDouble() ?: error("No step for $symbolEx"),
                    map["min"]?.toString()?.toDouble() ?: error("No min for $symbolEx")
            )
        }
    }
}

open class ExchangeConstants(
    val step: Double,
    val min: Double,
    val contractSize: Double = min,
    val maxMarketSize: Double? = null,
)

