package trading.exchanges.hyperliquid.socket

import io.micronaut.context.annotation.Value
import io.micronaut.scheduling.TaskScheduler
import jakarta.inject.Named
import jakarta.inject.Singleton
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import trading.exceptions.scheduleCatching
import trading.exchanges.socket.SocketSubscriber
import trading.storage.repos.StrategyRepository
import java.time.Duration.ofSeconds

@Singleton
class HyperLiquidNodeSubscriber(
    @Named("hyperLiquidKey") apiKey: String?,
    @Named("hyperLiquidWallet") private val wallet: String,
    @Value("\${hyperliquid.node.trades}") enabled: Bo<PERSON>an,
    @Value("\${hyperliquid.node.host}") address: String,
    listener: HyperLiquidNodeListener,
    taskScheduler: TaskScheduler,
    strategyRepository: StrategyRepository
) : SocketSubscriber("$address/ws", listener, taskScheduler, strategyRepository) {
    override val enabled: Boolean = !apiKey.isNullOrEmpty() && enabled

    override val log: Logger = LoggerFactory.getLogger(HyperLiquidNodeSubscriber::class.java)
    override val exchange = "hyperliquid"

    private val format = Json {
        encodeDefaults = true
        ignoreUnknownKeys = true
    }

    init {
        lateinit()
        taskScheduler.scheduleCatching(this, ofSeconds(4), ofSeconds(4)) {
            ping()
        }
    }

    override fun subscribeToAll() {
        if (subs.isEmpty()) {
            return
        }

        // subscribe to all trades for our wallet
        socket.send(format.encodeToString(SubscribeToTradesRequest(wallets = listOf(wallet))))
    }

    override fun sayHi() {
        // do nothing as it is public
    }

    private fun ping() {
        socket.send("ping")
    }

}

@Serializable
data class SubscribeToTradesRequest(
    val channel: String = "trades",
    val wallets: List<String>
)