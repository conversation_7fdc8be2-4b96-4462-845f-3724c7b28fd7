package trading.exchanges.hyperliquid.socket

import jakarta.inject.Singleton
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import okhttp3.WebSocket
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import trading.events.EventPublisher
import trading.events.ExExecutionEvent
import trading.events.Integrity
import trading.exchanges.hyperliquid.toTradeId
import trading.exchanges.socket.SocketListener
import trading.models.ExExecution
import trading.models.FeeAsset.Companion.MissingFee
import trading.models.OrdStatus
import trading.models.Side
import trading.models.SymbolEx
import java.time.Instant

@Singleton
class HyperLiquidNodeListener(
    private val eventPublisher: EventPublisher
) : SocketListener() {

    override val log: Logger = LoggerFactory.getLogger(HyperLiquidNodeListener::class.java)

    private val format = Json {
        encodeDefaults = true
        ignoreUnknownKeys = true
    }

    override fun internalOnMessage(text: String, socket: WebSocket) {
        val receivedAt = System.nanoTime()

        try {
            val msg = format.decodeFromString<TradeMessage>(text)
            val execution = ExExecution(
                toTradeId(msg.hash, msg.exOrderId, msg.startPos),
                msg.exOrderId,
                SymbolEx("hyperliquid", msg.symbol),
                OrdStatus.Missing,
                msg.size,
                msg.price,
                msg.botOrderId?.removePrefix("0x") ?: "unknown",
                Instant.ofEpochMilli(msg.timestamp),
                0.0,
                MissingFee,
                Side.parse(msg.side)
            )
            eventPublisher.publishBlocking(
                ExExecutionEvent(Integrity.New, receivedAt, listOf(execution))
            )
        } catch (e: Exception) {
            when (text) {
                "pong" -> {
                    // do nothing
                }

                """{"status":"ok","ch":"trades"}""" -> {
                    log.info("Subscribed to trades")
                }

                else -> {
                    log.error("Failed to parse: $text", e)
                }
            }
        }
    }
}

@Serializable
data class TradeMessage(
    @SerialName("c") val symbol: String,
    @SerialName("oid") val exOrderId: String,
    @SerialName("s") val side: String,
    @SerialName("cid") val botOrderId: String?,
    @SerialName("sp") val startPos: Double,
    @SerialName("sz") val size: Double,
    @SerialName("px") val price: Double,
    @SerialName("hash") val hash: String,
    @SerialName("time") val timestamp: Long
)