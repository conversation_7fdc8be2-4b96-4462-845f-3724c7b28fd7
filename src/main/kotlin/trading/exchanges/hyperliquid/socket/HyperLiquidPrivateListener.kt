package trading.exchanges.hyperliquid.socket

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import jakarta.inject.Named
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import okhttp3.WebSocket
import org.slf4j.LoggerFactory
import trading.arb.metrics.Metrics.Companion.logMetric
import trading.events.*
import trading.events.SettlementEvent.SettlementType.Funding
import trading.exchanges.hyperliquid.HyperLiquidApiResponse
import trading.exchanges.hyperliquid.HyperLiquidConnector
import trading.exchanges.hyperliquid.HyperliquidBaseConnector.Companion.toNewOrder
import trading.exchanges.hyperliquid.SuccessResponse
import trading.exchanges.hyperliquid.toTradeId
import trading.exchanges.hyperliquid.parseExchangeByCoin
import trading.exchanges.socket.SocketListener
import trading.metrics.MetricsLabels.Companion.LATENCY_LIMIT
import trading.models.*
import trading.models.FeeAsset.Companion.toFeeAsset
import trading.storage.caches.OrderCache
import trading.telegram.TelegramSender
import java.time.Instant

class HyperLiquidPrivateListener(
    @Named("hyperLiquidWallet") private val wallet: String,
    private val objectMapper: ObjectMapper,
    private val hyperLiquidConnector: HyperLiquidConnector,
    private val eventPublisher: EventPublisher,
    private val orderCache: OrderCache,
    private val telegramSender: TelegramSender
) : SocketListener() {

    private val exchange = "hyperliquid"

    override val log = LoggerFactory.getLogger(HyperLiquidPrivateListener::class.java)!!

    @OptIn(DelicateCoroutinesApi::class)
    override fun internalOnMessage(text: String, socket: WebSocket) {
        val receivedAt = System.nanoTime()

        try {
            val events = objectMapper.readValue<HLUserEventsResponse>(text)

            when (val data = events.data) {
                is HLFillsData -> {
                    // Trades
                    data.fills
                        // When hyperliquid liquidated another user position
                        // using us we receive a trade with filled liquidation
                        // field with the address that user. This is a regular
                        // trade for us. That's why we check liquidatedUser
                        // field.
                        .filter { it.liquidation?.liquidatedUser?.endsWith(wallet) != true }
                        .map { it.toExecution() }
                        .filter { it.symbolEx.exchange == exchange }
                        .let {
                            if (it.isNotEmpty()) {
                                eventPublisher.publishBlocking(ExExecutionEvent(Integrity.New, receivedAt, it))
                            }
                        }
                    // Liquidations
                    data.fills
                        .filter { it.liquidation?.liquidatedUser?.endsWith(wallet) == true }
                        .map { ExLiquidation(toTradeId(it.hash, it.orderId, it.startPosition), SymbolEx(exchange, it.coin), it.size, Side.parse(it.side)) }
                        .let {
                            if (it.isNotEmpty()) {
                                eventPublisher.publishBlocking(ExLiquidationEvent(Integrity.New, it))
                                log.info("Liquidation ws message: $text")
                            }
                        }
                }

                is HLFundingData -> {
                    eventPublisher.publishAsync(
                        SettlementEvent(
                            SymbolEx(exchange, data.funding.coin), data.funding.usdc.toDouble(), "USDC",
                            Funding
                        )
                    )
                }

                is HLPostData -> {
                    val request = orderCache.popRequest(data.requestId)

                    kotlin.runCatching {
                        if (request != null && data.response.payload is SuccessResponse) {
                            data.response.payload.toNewOrder(request.botOrder.botOrderId).exOrderId?.let { exOrderId ->
                                val latency = System.currentTimeMillis() - request.requestedOn
                                val confirmed = request.confirmCreation(exOrderId)
                                log.info("limit ${exOrderId}/${request.botOrder.botOrderId} confirmed [${latency / 1000.0} sec]")

                                logMetric(LATENCY_LIMIT, latency, exchange, request.botOrder.symbolEx.symbol)

                                if (!confirmed) {
                                    log.warn("Looks like order is superseded, cancelling")
                                    GlobalScope.launch {
                                        hyperLiquidConnector.cancelOrder(
                                            request.botOrder.botOrderId,
                                            request.botOrder.exOrderId,
                                            request.botOrder.symbolEx.symbol
                                        )
                                    }
                                }
                            }
                        } else {
                            error("Failed or unknown request")
                        }
                    }.onFailure {
                        if (it.message?.contains("Post only order would have immediately matched") == true) {
                            request?.rollback()
                        } else {
                            // in other cases we cant be sure that order was not created
                            telegramSender.warnOnce(
                                "HYPERLIQUID_LIMIT_INIT_ERROR", "Error while creating order: ${it.message.addSymbolNameToAssetId()}"
                            )
                            log.error("Hyperliquid response: $data")
                        }
                    }
                }
            }

        } catch (origEx: Exception) {
            try {
                if (text == "{\"channel\":\"pong\"}") {
                    return
                }

                val subscription = objectMapper.readValue<HLSubscriptionResponse>(text)
                if (subscription.data.subscription.type == "userEvents") {
                    log.info("Subscribed to $exchange trade notification")
                    return
                }
            } catch (ex: Exception) {
                log.warn("Unhandled socket message: $text with error $origEx")
            }
        }
    }

    private fun String?.addSymbolNameToAssetId(): String? = this?.let { hyperLiquidConnector.addSymbolNameToAssetId(this) }
}

// From channel userEvents
@JsonIgnoreProperties(ignoreUnknown = true)
data class HLUserEventsResponse(
    val channel: String,
    val data: HLResponseData
)

@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)
@JsonSubTypes(
    JsonSubTypes.Type(HLFillsData::class),
    JsonSubTypes.Type(HLFundingData::class),
    JsonSubTypes.Type(HLPostData::class)
)
sealed class HLResponseData

data class HLPostData(
    @JsonProperty("id") val requestId: Int,
    val response: HLPostDataPayload
) : HLResponseData()

data class HLFillsData(
    val fills: List<HLFill>
) : HLResponseData()

data class HLFundingData(
    val funding: HLFunding
) : HLResponseData()

data class HLPostDataPayload(
    val type: String,
    val payload: HyperLiquidApiResponse
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class HLLiquidation(
    val liquidatedUser: String,
    val markPx: Double,
    val method: String
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class HLFunding(
    val time: Long,
    val coin: String,
    val usdc: String,
    val szi: String,
    val fundingRate: String,
)

/**
 * @param coin could look like BTC, ETH, SOL for perps, but looks like `@{index}` for spot.
 * See [trading.exchanges.hyperliquid.HLInternalFullSymbol]
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class HLFill(
    val coin: String,
    @JsonProperty("px") val price: Double,
    @JsonProperty("sz") val size: Double,
    val side: String,
    val time: Long,
    val startPosition: Double,
    val dir: String,
    val closedPnl: Double,
    val hash: String,
    @JsonProperty("oid") val orderId: String,
    val crossed: Boolean,
    val fee: Double,
    val feeToken: String,
    @JsonProperty("cloid") val botOrderId: String? = null,
    val liquidation: HLLiquidation? = null
)

fun HLFill.toExecution(transformedCoin: String? = null) = ExExecution(
    id = toTradeId(hash, orderId, startPosition),
    ordId = orderId,
    symbolEx = SymbolEx(parseExchangeByCoin(coin), transformedCoin ?: coin),
    ordStatus = OrdStatus.Missing,
    contracts = size,
    price = price,
    botOrderId = botOrderId?.removePrefix("0x") ?: "",
    timeStamp = Instant.ofEpochMilli(time),
    fee = fee,
    feeAsset = feeToken.toFeeAsset(),
    side = Side.parse(side) // A - Sell, B - Buy
)

data class HLSubscriptionResponse(
    @JsonProperty("channel") val channel: String, @JsonProperty("data") val data: HLSubscriptionData
)

data class HLSubscriptionData(
    @JsonProperty("method") val method: String, @JsonProperty("subscription") val subscription: HLSubscription
)

data class HLSubscription(
    @JsonProperty("type") val type: String
)


// Channel userEvents
// Funding:
// {"channel":"user","data":{"funding":{"time":1721512800049,"coin":"BTC","usdc":"0.002882","szi":"-0.00067","fundingRate":"0.00006413","nSamples":null}}}
// {"channel":"user","data":{"funding":{"time":1721512800049,"coin":"ETH","usdc":"-0.000558","szi":"0.0127","fundingRate":"0.0000125","nSamples":null}}}

// Trade
// {"channel":"user","data":{"fills":[{"coin":"BTC","px":"67096.0","sz":"0.00067","side":"B","time":1721510221938,"startPosition":"-0.00067","dir":"Close Short","closedPnl":"0.30552","hash":"0xf36517ceda2c4ac7b37d040dd2da98015d002b514452f73f69cd2adc0e740cdd","oid":30555366683,"crossed":true,"fee":"0.015734","tid":947259839674097,"feeToken":"USDC"}]}}

// Liquidation (partial, with several fiils)
// {'channel': 'user', 'data': {'fills': [{'coin': 'kPEPE', 'px': '0.007188', 'sz': '205645.0', 'side': 'B', 'time': 1742340189615, 'startPosition': '-1269929.0', 'dir': 'Close Short', 'closedPnl': '-50.99996', 'hash': '0x8fbe9d95fe2f028e15a70414aaffba014300bcc6f6cbc7126c9b3b3e6cae22cf', 'oid': 26684372363, 'crossed': True, 'fee': '0.517361', 'tid': 431278931203726, 'liquidation': {'liquidatedUser': '******************************************', 'markPx': '0.007164', 'method': 'market'}, 'feeToken': 'USDC'}, {'coin': 'kPEPE', 'px': '0.007207', 'sz': '403403.0', 'side': 'B', 'time': 1742340189615, 'startPosition': '-1064284.0', 'dir': 'Close Short', 'closedPnl': '-107.708601', 'hash': '0x8fbe9d95fe2f028e15a70414aaffba014300bcc6f6cbc7126c9b3b3e6cae22cf', 'oid': 26684372363, 'crossed': True, 'fee': '1.017563', 'tid': 1067442109193184, 'liquidation': {'liquidatedUser': '******************************************', 'markPx': '0.007164', 'method': 'market'}, 'feeToken': 'USDC'}, {'coin': 'kPEPE', 'px': '0.00722', 'sz': '360040.0', 'side': 'B', 'time': 1742340189615, 'startPosition': '-660881.0', 'dir': 'Close Short', 'closedPnl': '-100.8112', 'hash': '0x8fbe9d95fe2f028e15a70414aaffba014300bcc6f6cbc7126c9b3b3e6cae22cf', 'oid': 26684372363, 'crossed': True, 'fee': '0.909821', 'tid': 103445619192673, 'liquidation': {'liquidatedUser': '******************************************', 'markPx': '0.007164', 'method': 'market'}, 'feeToken': 'USDC'}, {'coin': 'kPEPE', 'px': '0.007242', 'sz': '300841.0', 'side': 'B', 'time': 1742340189615, 'startPosition': '-300841.0', 'dir': 'Close Short', 'closedPnl': '-90.853982', 'hash': '0x8fbe9d95fe2f028e15a70414aaffba014300bcc6f6cbc7126c9b3b3e6cae22cf', 'oid': 26684372363, 'crossed': True, 'fee': '0.762541', 'tid': 1058915166542213, 'liquidation': {'liquidatedUser': '******************************************', 'markPx': '0.007164', 'method': 'market'}, 'feeToken': 'USDC'}]}}
// ------------------------------------------------------------------------------------------------
// Channel userFills (we don't use it because we don't need snapshot and it sends slower than userEvents
// Liquidation:
// {"channel":"userFills","data":{"user":"******************************************","fills":[{"coin":"BTC","px":"67573.0","sz":"0.00065","side":"B","time":1721575950944,"startPosition":"-0.00065","dir":"Close Short","closedPnl":"-0.46735","hash":"0x240476803b3fddb55838040dde06370000f3d5042bbceac4bfa362612b1a43dc","oid":30615316898,"crossed":true,"fee":"0.015372","tid":30711019041432,"liquidation":{"liquidatedUser":"******************************************","markPx":"67531.0","method":"market"},"feeToken":"USDC"}]}}
// Trade:
// {"channel":"userFills","data":{"user":"******************************************","fills":[{"coin":"BTC","px":"67096.0","sz":"0.00067","side":"B","time":1721510221938,"startPosition":"-0.00067","dir":"Close Short","closedPnl":"0.30552","hash":"0xf36517ceda2c4ac7b37d040dd2da98015d002b514452f73f69cd2adc0e740cdd","oid":30555366683,"crossed":true,"fee":"0.015734","tid":947259839674097,"feeToken":"USDC"}]}}
// New order:
// {"channel":"post","data":{"id":965068002,"response":{"type":"action","payload":{"status":"ok","response":{"type":"order","data":{"statuses":[{"resting":{"oid":79873309418,"cloid":"0x1deb18bd13f0e5123985c4e577777777"}}]}}}}}}
