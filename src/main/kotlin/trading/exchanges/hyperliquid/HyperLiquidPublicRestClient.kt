package trading.exchanges.hyperliquid

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import com.github.kittinunf.fuel.core.extensions.jsonBody
import io.micronaut.context.annotation.Value
import jakarta.inject.Singleton
import trading.exchanges.ApiResponse
import trading.exchanges.FuelRestClient
import trading.exchanges.hyperliquid.socket.HLFill
import trading.models.*
import java.time.Instant

@Singleton
class HyperLiquidPublicRestClient(
    @Value("\${hyperliquid.address}") address: String,
    objectMapper: ObjectMapper
) : FuelRestClient(
    address,
    objectMapper
) {

    suspend fun getCandles(
        coin: String,
        interval: Int,
        startTime: Long,
        endTime: Long
    ): ApiResponse<List<HLCandle>> = manager.post("/info")
        .jsonBody(
            "{\"type\":\"candleSnapshot\", " +
                    "\"req\": " +
                    "{\"coin\": \"$coin\", " +
                    "\"interval\": \"${interval}m\", " +
                    "\"startTime\": $startTime, " +
                    "\"endTime\": $endTime" +
                    "}}".trimIndent()
        )
        .execute()

    suspend fun getBalancesPositions(
        walletAddress: String
    ): ApiResponse<HLMarginSummaryResponse> = manager.post("/info")
        .jsonBody("{\"type\":\"clearinghouseState\",\"user\": \"$walletAddress\"}")
        .execute()

    suspend fun getSpotBalancesPositions(
        walletAddress: String
    ): ApiResponse<HLSpotBalancesResponse> = manager.post("/info")
        .jsonBody("{\"type\":\"spotClearinghouseState\",\"user\": \"$walletAddress\"}")
        .execute()

    suspend fun getAssetsMeta(): ApiResponse<HLAssets> = manager.post("/info")
        .jsonBody("{\"type\":\"meta\"}")
        .execute()

    suspend fun getSpotAssetsMeta(): ApiResponse<HLSpotAssetsMetaResponse> = manager.post("/info")
        .jsonBody("""{"type":"spotMeta"}""")
        .execute()

    suspend fun getAssetCtxMeta(): ApiResponse<List<Any>> = manager.post("/info")
        .jsonBody("{\"type\":\"metaAndAssetCtxs\"}")
        .execute()

    suspend fun getRateLimits(
        walletAddress: String
    ): ApiResponse<HLUsageStats> = manager.post("/info")
        .jsonBody("{\"type\":\"userRateLimit\",\"user\": \"$walletAddress\"}")
        .execute()

    suspend fun getOrderBook(
        coin: String
    ): ApiResponse<HLOrderBook> = manager.post("/info")
        .jsonBody("{\"type\":\"l2Book\",\"coin\": \"$coin\"}")
        .execute()

    suspend fun getOpenOrders(
        walletAddress: String
    ): ApiResponse<List<HLOrder>> = manager.post("/info")
        .jsonBody("{\"type\":\"openOrders\",\"user\": \"$walletAddress\"}")
        .execute()

    suspend fun getOrder(
        botOrderId: String,
        exOrderId: String?,
        walletAddress: String
    ): ApiResponse<HLNestedOrderWrapper> = manager.post("/info")
        .jsonBody("{\"type\":\"orderStatus\",\"user\":\"$walletAddress\",\"oid\":${exOrderId ?: "\"$botOrderId\""}}")
        .execute()

    suspend fun getExecutions(
        walletAddress: String
    ): ApiResponse<List<HLFill>> = manager.post("/info")
        .jsonBody("{\"type\":\"userFills\",\"user\":\"$walletAddress\"}")
        .execute()
}

//region order status
data class HLOrderWrapper(
    val order: HLOrder,
    val status: String //open, filled, canceled, triggered, rejected, marginCanceled
) {
    companion object {
        fun String.toOrdStatus() = when (this) {
            "open", "triggered" -> OrdStatus.New
            "filled" -> OrdStatus.Filled
            else -> OrdStatus.Canceled
        }
    }
}

data class HLNestedOrderWrapper(
    val order: HLOrderWrapper
)

/**
 * @param coin BTC, ETH for perps futures and @{index}, PURR/USDC for spot
 */
class HLOrder(
    @JsonProperty("coin") val coin: String,
    @JsonProperty("side") val side: String,
    @JsonProperty("limitPx") val price: Double,
    @JsonProperty("sz") val size: Double,
    @JsonProperty("oid") val orderId: Long,
    @JsonProperty("origSz") val originalSize: Double,
    @JsonProperty("cloid") val hexBotOrderId: String? = null
){
    fun toFullOrder(transformedCoin: String? = null): FullOrder = FullOrder(
        "" + orderId,
        SymbolEx(parseExchangeByCoin(coin), transformedCoin ?: coin),
        if (side == "B") Side.Buy else Side.Sell,
        size,
        originalSize - size,
        price,
        OrdType.Limit,
        OrdStatus.Missing,
        hexBotOrderId ?: "000000"
    )
}
//endregion

//region orderbook (for quote)
data class HLOrderBook(
    val coin: String,
    val time: Long,
    val levels: List<List<HLPriceLevel>>
)

data class HLPriceLevel(
    @JsonProperty("px") val price: Double,
    @JsonProperty("sz") val size: Double,
    @JsonProperty("n") val numberOfOrders: Int
)
//endregion

//region rate limits
data class HLUsageStats(
    @JsonProperty("cumVlm") val cumulativeVolume: Double,
    @JsonProperty("nRequestsUsed") val requestsUsed: Int,
    @JsonProperty("nRequestsCap") val requestsCap: Int
)
//endregion

//region balances and positions
data class HLMarginSummaryResponse(
    val marginSummary: HLSummary,
    val crossMarginSummary: HLSummary,
    val crossMaintenanceMarginUsed: String,
    val withdrawable: String,
    val assetPositions: List<HLAssetPosition>,
    val time: Long
)

data class HLSummary(
    val accountValue: Double,
    val totalNtlPos: Double,
    val totalRawUsd: Double,
    val totalMarginUsed: Double
)

data class HLAssetPosition(
    val type: String,
    val position: HLPosition
)

data class HLPosition(
    val coin: String,
    val szi: Double,
    val leverage: HLLeverage,
    val entryPx: Double,
    val positionValue: String,
    val unrealizedPnl: String,
    val returnOnEquity: String,
    val liquidationPx: Double?,
    val marginUsed: String,
    val maxLeverage: Int,
    val cumFunding: HLCumFunding
)

data class HLLeverage(
    val type: String,
    val value: Int
)

data class HLCumFunding(
    val allTime: String,
    val sinceOpen: String,
    val sinceChange: String
)
//endregion

//region supported assets
data class HLAssets(
    val universe: List<HLUniverse>
)

data class HLUniverse(
    val name: String,
    val maxLeverage: Int,
    val szDecimals: Int,
    val isDelisted: Boolean?
)
//endregion

//region candles
class HLCandle(
    t: Long, o: Double, c: Double,
    h: Double, l: Double, v: Double,
) : ExCandle(l, h, o, c, v, Instant.ofEpochMilli(t))
//endregion


/**
 * @link https://hyperliquid.gitbook.io/hyperliquid-docs/for-developers/api/info-endpoint/spot#retrieve-spot-metadata
 */
data class HLSpotAssetsMetaResponse(
    val universe: List<HLItemUniverse>,
    val tokens: List<HLItemToken>,
){
    /**
     * @param tokens contains a value in 0'th index (token ID) helping to map to data from "tokens" by "tokens[*].index"
     * @param name coin name is in the pattern "@{index}" excepting PURR/USDC
     * @param index is mainnet/testnet spot id
     */
    data class HLItemUniverse(
        val tokens: List<TokenIndexId>,
        val name: String,
        val index: Int,
    ){
        /**
         * Mainnet/testnet token id
         */
        val tokenIndexId = tokens[0]
    }

    /**
     * @param name is a token name like UBTC, UETH, USOL and so on.
     * @param szDecimals is amount of digits in fraction part after dot
     * @param index is ID helping to map data from the key "universe" to this key by "universe[*].tokens[0]".
     * @param tokenId is a token id in HEX format like 0x49b67c39f5566535de22b29b0e51e685.
     */
    data class HLItemToken(
        val name: String,
        val szDecimals: Int,
        val index: TokenIndexId,
        val tokenId: String,
    )
}

data class HLSpotBalancesResponse(val balances: List<HLSpotBalance>) {
    data class HLSpotBalance(
        val coin: String,
        val token: TokenIndexId,
        val hold: Double,
        val total: Double,
        val entryNtl: Double,
    )
}

/**
 * Represents token ID in terms of
 */
typealias TokenIndexId = Int

fun List<HLOrder>.spot() = this.filter { it.coin.startsWith("@") || it.coin == "PURR/USDC" }
fun List<HLOrder>.perps() = this.filterNot { it.coin.startsWith("@") || it.coin == "PURR/USDC" }

fun parseExchangeByCoin(coin: String): String {
    if (coin == "") {
        throw IllegalStateException("Empty coin")
    }
    return when {
        coin[0] == '@' || coin.uppercase() == "PURR/USDC" -> "hyperliquid-spot"
        else -> "hyperliquid"
    }
}