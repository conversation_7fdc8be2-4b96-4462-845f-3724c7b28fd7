package trading.utils

import java.math.RoundingMode
import kotlin.math.pow
import kotlin.math.roundToLong

// TODO is it possible to get rid of bigdecimal for this operations ?
fun Double.roundTo(step: Double) = ((this / step).roundToLong() * step).scaleTo(step.scale())

fun Double.scaleTo(scale: Int) = this.toBigDecimal().setScale(scale, RoundingMode.HALF_EVEN).toDouble()

fun Double.toPrettyString(scale: Int = 10): String = this.toBigDecimal().setScale(scale, RoundingMode.HALF_EVEN).stripTrailingZeros().toPlainString()

fun Double?.orZero(): Double = this ?: 0.0

fun Double?.orOne(): Double = this ?: 1.0

/**
 * Determines the scale (number of decimal places) of a Double value.
 * 
 * This function converts the Double to a BigDecimal, strips trailing zeros,
 * and returns the scale. For zero values, it returns 0. For values with a
 * negative scale (e.g., 100.0), it also returns 0.
 * 
 * @return The number of decimal places in the Double value.
 * 
 * Examples:
 * - 0.0.scale() returns 0
 * - 1.0.scale() returns 0
 * - 1.5.scale() returns 1
 * - 1.25.scale() returns 2
 * - 0.001.scale() returns 3
 * - 1000.0.scale() returns 0
 */
fun Double.scale(): Int = if (this == 0.0) 0 else {
    this.toBigDecimal().stripTrailingZeros().scale().let {
        if (it < 0) 0 else it
    }
}

/** may be this implementation is better?
fun Double.scaleTo(scale: Int): Double {
val factor = 10.0.pow(scale.toDouble())
return (this * factor).roundToInt() / factor
}
 */


/**
 * Converts an integer precision value to its decimal representation (satoshies).
 * 
 * This function calculates the smallest decimal unit based on the provided precision.
 * For example, a precision of 2 means the smallest unit is 0.01, while a precision
 * of 4 means the smallest unit is 0.0001.
 * 
 * @return The decimal representation of the smallest unit for the given precision.
 * 
 * Examples:
 * - 2.toSatoshies() returns 0.01
 * - 4.toSatoshies() returns 0.0001
 * - 8.toSatoshies() returns 0.00000001
 */
fun Int.toSatoshies(): Double = 0.1.div(10.0.pow(this - 1))
