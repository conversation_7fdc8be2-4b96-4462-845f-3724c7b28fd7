package trading.trading.holders

import io.micronaut.context.ApplicationContext
import io.micronaut.runtime.event.ApplicationShutdownEvent
import io.micronaut.runtime.event.annotation.EventListener
import io.micronaut.scheduling.TaskScheduler
import io.micronaut.scheduling.annotation.Scheduled
import jakarta.annotation.PostConstruct
import jakarta.inject.Singleton
import kotlinx.coroutines.*
import org.slf4j.LoggerFactory
import trading.exceptions.TemporaryException
import trading.exceptions.catcher
import trading.exceptions.catching
import trading.exceptions.scheduleCatching
import trading.exchanges.ExchangeRouter
import trading.models.*
import trading.models.StdMethod.*
import trading.storage.repos.SettingsRepository
import trading.storage.repos.StrategyRepository
import trading.telegram.TelegramSender
import trading.trading.common.ShutdownManager
import trading.utils.*
import java.lang.Thread.sleep
import java.time.Duration.ofSeconds
import java.time.Instant
import java.time.temporal.ChronoUnit.MINUTES
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ScheduledFuture
import kotlin.math.absoluteValue
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sqrt
import kotlin.system.exitProcess
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.toJavaDuration

@Singleton
class ThresholdsHolder(
    private val exchangeRouter: ExchangeRouter,
    private val orderBookHolder: OrderBookHolder,
    private val fundingHolder: FundingHolder,
    private val taskScheduler: TaskScheduler,
    private val strategyRepository: StrategyRepository,
    private val telegramSender: TelegramSender,
    context: ApplicationContext,
    private val settingsRepository: SettingsRepository
) {

    companion object {
        private val log = LoggerFactory.getLogger(ThresholdsHolder::class.java)

        private const val STD_FILTERHIGH_TAKE = 0.90 // 90%
        private const val LESS_CANDLES_RATIO = 5
    }

    private val shutdownManager: ShutdownManager by lazy { context.getBean(ShutdownManager::class.java) }

    private val maStdOpenCloseCache = ConcurrentHashMap<String, MaStdOpenCloseSize>()

    private val candlesCache = HashMap<SymbolEx, MutableList<ExCandle>>()

    private val threads: MutableList<ScheduledFuture<*>> = mutableListOf()

    @EventListener
    fun stop(event: ApplicationShutdownEvent) {
        threads.forEach { it.cancel(true) }
    }

    @PostConstruct
    fun startTasks() {
        taskScheduler.scheduleCatching(this, ofSeconds(6), ofSeconds(60)) {
            // candles thread
            rebuildMaStd()
        }.also { threads.add(it) }
    }

    @Scheduled(initialDelay = "5m")
    fun checkThreshold() {
        try {
            strategyRepository.findAll().forEach {
                thresholds(it)
            }
            log.info("Thresholds checked")
        } catch (e: TemporaryException) {
            log.error("No thresholds after 5 minutes, restarting: ${e.message}")
            telegramSender.warn("No thresholds after 5 minutes, restarting: ${e.message}")
            shutdownManager.pause()
            GlobalScope.launch(LOW_PRIORITY_DISPATCHER) {
                delay(3000)
                exitProcess(0)
            }
        }
    }

    fun rebuildMaStd() {
        catching(this@ThresholdsHolder) {
            maStdOpenCloseCache.putAll(strategyRepository.findAll().map { str -> str.uuid to calculate(str) })
        }
    }

    private fun updateAndGetCandles(
        symbolEx: SymbolEx,
        count: Short,
        interval: Interval,
        time: Instant
    ): List<ExCandle> {
        val candles = candlesCache.computeIfAbsent(symbolEx) {
            runBlocking(Dispatchers.IO + catcher(this)) {
                delay(500) // on multi-strategies bots there are sometimes 429 error
                exchangeRouter.getCandles(symbolEx, count, interval).toMutableList()
            }
        }

        val quote = orderBookHolder.getLastQuote(symbolEx, true)!!
        val price = (quote.ask + quote.bid) / 2.0

        if (log.isDebugEnabled) {
            log.debug("Adding new candle of $symbolEx with price: $price")
        }

        if (candles.isNotEmpty() && candles.size >= count) {
            candles.removeLast() // removing oldest
        }
        candles.add(0, ExCandle(price, price, price, price, 0.0, time)) // adding new
        return candles
    }

    private fun calculate(strategy: Strategy): MaStdOpenCloseSize {
        val zipped = synchronized(this) {
            val time = Instant.now()

            var leg1Candles = updateAndGetCandles(
                strategy.legs.leg1.symbolEx,
                strategy.candles,
                strategy.interval,
                time
            )
            var leg2Candles = updateAndGetCandles(
                strategy.legs.leg2.symbolEx,
                strategy.candles,
                strategy.interval,
                time
            )

            if (leg1Candles.size != leg2Candles.size || leg1Candles.size.toShort() != strategy.candles) {
                val maxPossible = min(leg1Candles.size, leg2Candles.size)

                log.warn("Candles count mismatch for $strategy (${leg1Candles.size} and ${leg2Candles.size}), taking $maxPossible / ${strategy.candles}")

                leg1Candles = leg1Candles.take(maxPossible).toMutableList()
                leg2Candles = leg2Candles.take(maxPossible).toMutableList()

                candlesCache[strategy.legs.leg1.symbolEx] = leg1Candles
                candlesCache[strategy.legs.leg2.symbolEx] = leg2Candles
            }

            val minutes = MINUTES.between(leg1Candles.last().timestamp, leg2Candles.last().timestamp).toInt()
            if (minutes != 0) {
                if (minutes > 0) {
                    leg1Candles = leg1Candles.dropLast(minutes)
                    leg2Candles = leg2Candles.drop(minutes)
                } else {
                    leg1Candles = leg1Candles.drop(minutes.absoluteValue)
                    leg2Candles = leg2Candles.dropLast(minutes.absoluteValue)
                }

                candlesCache[strategy.legs.leg1.symbolEx] = leg1Candles.toMutableList()
                candlesCache[strategy.legs.leg2.symbolEx] = leg2Candles.toMutableList()

                log.warn("Candles shifted for $strategy, taking equal interval of ${leg1Candles.size} candles")
            }

            if (leg1Candles.last().timestamp != leg2Candles.last().timestamp) {
                log.warn("Candles still shifted for $strategy (${leg1Candles.last().timestamp} and ${leg2Candles.last().timestamp}), rebuilding")
                candlesCache.clear()
                sleep(500.milliseconds.toJavaDuration()) // need to wait a bit, because it can loop
                throw TemporaryException("Candles timestamp mismatch")
            }

            val zipped = leg1Candles.zip(leg2Candles).drop(1) // dropping current candle

            if (log.isDebugEnabled) {
                log.debug("Zipped candles for ${strategy.uuid}: $zipped")
            }

            zipped
        }

        // Difference in closing prices between legs (avg or median)
        var ma = zipped
            .map { (l1, l2) -> (l1.close - l2.close) }
            .let { maSeries ->
                when (strategy.stdMethod) {
                    MEDIAN, MEDIANLESSCANDLES -> maSeries
                        .median()

                    else -> maSeries
                        .average()
                }
            }

        // Take the first close price from the first leg
        val price = zipped.first().first.close

        // How much MA is different from the price in percent
        val maDeviation = ma.absoluteValue / price

        // Defence against from high deviations originated by anomalies
        if (maDeviation > strategy.maLimit) {
            val newMa = price * strategy.maLimit.let {
                if (ma < 0.0) it.unaryMinus() else it
            }
            log.warnOnce("HIGH_MA_DEVIATION", "MA exceeded threshold ($ma), limiting to $newMa")
            ma = newMa
        }

        val std = when (strategy.stdMethod) {
            FILTERHIGH -> {
                val filtered = zipped.asSequence()
                    .map { (l1, l2) -> (ma - (l1.close - l2.close)).absoluteValue }
                    .sorted()
                    // Take the first 90% of the list and ignore the last 10%
                    // in order to value price difference volatility more
                    // accurate. This helps us to ignore anomalies.
                    .take((zipped.count() * STD_FILTERHIGH_TAKE).toInt())
                    .toList()

                val stdsSum = filtered.sumOf { std -> std * std }

                val size = filtered.size - 1
                val divider = if (size > 0) size else 1

                sqrt(stdsSum / divider)
            }

            MEDIAN -> {
                zipped
                    .map { (l1, l2) -> (ma - (l1.close - l2.close)).absoluteValue }
                    .median()
            }

            STANDART -> {
                val stdsSum = zipped
                    .asSequence()
                    .map { (l1, l2) -> (ma - (l1.close - l2.close)).absoluteValue }
                    .map { std -> std * std }
                    .sum()

                val size = zipped.size - 1
                val divider = if (size > 0) size else 1

                sqrt(stdsSum / divider)
            }

            MEDIANLESSCANDLES -> {
                val lessCandles = zipped
                    .take(zipped.count() / LESS_CANDLES_RATIO)

                val lessMa = lessCandles
                    .map { (l1, l2) -> (l1.close - l2.close) }
                    .median()

                lessCandles
                    .map { (l1, l2) -> (lessMa - (l1.close - l2.close)).absoluteValue }
                    .median()
            }
        }

        val open = max(strategy.openPriceKoef * price, strategy.openStdKoef * std)
        val close = max(strategy.closePriceKoef * price, strategy.closeStdKoef * std)

        val scale = max(
            exchangeRouter.getConstants(strategy.legs.leg1.symbolEx).step.scale(),
            exchangeRouter.getConstants(strategy.legs.leg2.symbolEx).step.scale()
        )
        log.info("[${strategy.uuid}] calc MaStdOpenCloseSize: leg1.step=${exchangeRouter.getConstants(strategy.legs.leg1.symbolEx).step}, leg2.step=${exchangeRouter.getConstants(strategy.legs.leg2.symbolEx).step}")

        val (ordSize, ordSizeInContracts) = calcOrdSize(strategy, price, std)

//        if(log.isDebugEnabled){
            log.info("[${strategy.uuid}] calc MaStdOpenCloseSize: ma=$ma, std=$std, open=$open, close=$close, ordSize=$ordSize, ordSizeInContracts=$ordSizeInContracts")
            log.info("[${strategy.uuid}] calc MaStdOpenCloseSize: open=max(strategy.openPriceKoef*price, strategy.openStdKoef*std)=max(${strategy.openPriceKoef}*${price},${strategy.openStdKoef}*${std})=$open")
            log.info("[${strategy.uuid}] calc MaStdOpenCloseSize: close=max(strategy.closePriceKoef*price, strategy.closeStdKoef*std)=max(${strategy.closePriceKoef}*${price},${strategy.closeStdKoef}*${std})=$close")
//        }

        return MaStdOpenCloseSize(
            ma.scaleTo(scale), std.scaleTo(scale),
            open.scaleTo(scale), close.scaleTo(scale),
            ordSize, ordSizeInContracts
        )
    }

    private fun calcOrdSize(strategy: Strategy, price: Double, std: Double): Pair<Double, Double> {
        // Limit strategy order size to maximum MARKET order size of second leg
        val leg2 = strategy.legs.leg2.symbolEx
        val maxOrdSizeInContracts = exchangeRouter.getConstants(leg2).maxMarketSize
        val maxOrdSize = if (maxOrdSizeInContracts != null && !leg2.spot) {
            kotlin.runCatching {
                exchangeRouter.contractsToUsd(strategy.legs.leg2.symbolEx, maxOrdSizeInContracts) - 100.0
            }.getOrDefault(Double.MAX_VALUE)
        } else {
            Double.MAX_VALUE
        }

        val strOrdSize = min(strategy.ordSize, maxOrdSize)
        if (strOrdSize != strategy.ordSize) {
            log.infoOnce("max_order_size_${strategy.uuid}", "Ord size reduced for $leg2 to $strOrdSize usd")
        }

        // Price could equal zero when this is closing price from the first
        // candle at the beginning of trading by a token.
        val ordSize = if (strategy.ordSizeStdKoef == 0.0 || price == 0.0) {
            strOrdSize
        } else {
            // ordSize = ordSizeStdKoef * (std/price) with top limit strOrdSize
            min(std * (strategy.ordSizeStdKoef) / price, strOrdSize)
        }
        val ordSizeInContracts = exchangeRouter.usdToContracts(strategy.legs.leg1.symbolEx, ordSize)

        return Pair(ordSize, ordSizeInContracts)
    }

    fun thresholds(strategy: Strategy): Thresholds {
        val msoc = maStdOpenCloseCache[strategy.uuid]
            ?: throw TemporaryException("No thresholds for ${strategy.toShortString()}")

        val price = orderBookHolder.getLastQuote(strategy.legs.leg1.symbolEx, false)?.bid
            ?: throw TemporaryException("No quotes for ${strategy.legs.leg1.symbolEx}")

        val move = max(
            strategy.movePriceKoef * price,
            exchangeRouter.getConstants(strategy.legs.leg1.symbolEx).step
        )

//        log.info("ThresholdsHolder.thresholds: move=max(strategy.movePriceKoef*price,exchangeRouter.getConstants(strategy.legs.leg1.symbolEx).step)")
//        log.info("ThresholdsHolder.thresholds: move=max(${strategy.movePriceKoef}*$price,${exchangeRouter.getConstants(strategy.legs.leg1.symbolEx).step})=$move")
        //todo: посмотреть откуда беруться данные в step, проблема очень низкой цены в ордере на покупку в нем

        val scale = msoc.open.scale()

        val range = strategy.rangeKoef * move

        return Thresholds(
            msoc.open, msoc.close, msoc.std,
            msoc.ma, range.scaleTo(scale), move.scaleTo(scale),
            msoc.ordSize, msoc.ordSizeInContracts
        ).let { thr ->
            val fundingSkew = fundingHolder.getFundingSkew(strategy.legs)
            if (fundingSkew.absoluteValue > settingsRepository.fundingAdd) {
                // we only add funding if it exceeds settings.fundingLevel[0]
                if (fundingSkew < 0.0) {
                    // we need to open long and be careful with short
                    thr.copy(addAsk = fundingSkew.absoluteValue * price)
                } else {
                    thr.copy(decBid = fundingSkew.absoluteValue * price)
                }
            } else {
                thr
            }
        }.let { thr ->
            if (strategy.adjustByMove) {
                thr.copy(
                    open = (thr.open + thr.move).scaleTo(scale),
                    close = (thr.close + thr.move).scaleTo(scale)
                )
            } else {
                thr
            }
        }
    }

    private fun List<Double>.median(): Double = this.sorted().let {
        when {
            it.isEmpty() -> 0.0
            it.size == 1 -> it.first()
            it.size % 2 == 0 -> (it[it.size / 2] + it[(it.size - 1) / 2]) / 2.0
            else -> it[it.size / 2]
        }
    }
}

/**
 * @param ordSize is an order size in USD
 * @param ordSizeInContracts is an order size in contracts (tokens)
 */
data class Thresholds(
    val open: Double,
    val close: Double,
    val std: Double,
    val ma: Double,
    val range: Double,
    val move: Double,
    val ordSize: Double,
    val ordSizeInContracts: Double,
    val decBid: Double = 0.0, // price - decBid
    val addAsk: Double = 0.0 // price + addAsk
) {
    override fun toString() =
        "Thresholds (open = ${open.toPrettyString()}, close = ${close.toPrettyString()}, STD = ${std.toPrettyString()}, MA = ${ma.toPrettyString()}, range = ${range.toPrettyString()}, move = ${move.toPrettyString()}${
            if (addAsk != 0.0) {
                ", add ask = ${addAsk.toPrettyString()}"
            } else if (decBid != 0.0) {
                ", dec bid = ${decBid.toPrettyString()}"
            } else {
                ""
            }
        })"

    fun toShortString() =
        "open: ${open.toPrettyString()} | close: ${close.toPrettyString()} | STD: ${std.toPrettyString()} | MA: ${ma.toPrettyString()} | range: ${range.toPrettyString()} | move: ${move.toPrettyString()} | ordSize: ${
            ordSize.scaleTo(
                0
            )
        }${
            if (addAsk != 0.0) {
                " | add ask: ${addAsk.toPrettyString()}"
            } else if (decBid != 0.0) {
                " | dec bid: ${decBid.toPrettyString()}"
            } else {
                ""
            }
        }"
}

private data class MaStdOpenCloseSize(
    val ma: Double, val std: Double,
    val open: Double, val close: Double,
    val ordSize: Double, val ordSizeInContracts: Double
)