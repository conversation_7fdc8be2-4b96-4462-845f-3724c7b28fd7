package trading.trading.common

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import io.lettuce.core.api.StatefulRedisConnection
import io.micronaut.runtime.event.ApplicationShutdownEvent
import io.micronaut.runtime.event.annotation.EventListener
import jakarta.annotation.PostConstruct
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory
import trading.arb.metrics.Metrics.Companion.logMetric
import trading.events.*
import trading.exceptions.catching
import trading.exchanges.ExchangeRouter
import trading.metrics.MetricsContext
import trading.metrics.MetricsContext.MetricsContextKey.TRADE
import trading.metrics.MetricsLabels.Companion.LATENCY_TRADE
import trading.models.*
import trading.storage.caches.DbOrdLegId
import trading.storage.caches.OrderCache
import trading.storage.models.Stage
import trading.storage.repos.FirstLegPositionRepository
import trading.storage.repos.RedisConnector
import trading.storage.repos.StrategyRepository
import trading.telegram.TelegramParams
import trading.telegram.TelegramSender
import trading.telegram.notifications.TradeReport
import trading.trading.holders.OrderBookHolder
import trading.trading.holders.ThresholdsHolder
import trading.utils.MaxSizeHashMap
import trading.utils.doIfTrueOrNull
import trading.utils.scaleTo
import java.lang.System.currentTimeMillis
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.absoluteValue

@Singleton
class ExExecutionHandler(
    private val orderCache: OrderCache,
    private val firstLegPositionRepository: FirstLegPositionRepository,
    private val strategyRepository: StrategyRepository,
    private val orderBookHolder: OrderBookHolder,
    private val meansHolder: ThresholdsHolder,
    private val eventPublisher: EventPublisher,
    private val telegramParams: TelegramParams,
    private val telegramSender: TelegramSender,
    private val exchangeRouter: ExchangeRouter,
    private val connection: StatefulRedisConnection<String, String>,
    objectMapper: ObjectMapper,
) : EventConsumer, RedisConnector(objectMapper) {

    companion object {
        private const val LAST_EXECUTIONS_NAMESPACE = "last-trades"
    }

    private val log = LoggerFactory.getLogger(ExExecutionHandler::class.java)

    /**
     * @param acked stores already counted orders trades so that the bot not to
     * count them twice. These trades received as fast as possible for trading
     * purpose and may not contain all data.
     */
    private val acked: ConcurrentHashMap<SymbolEx, MutableSet<Int>> = strategyRepository.findAll()
        .map { listOf(it.legs.leg1.symbolEx, it.legs.leg2.symbolEx) }
        .flatten()
        .associateWith { Collections.synchronizedSet(MaxSizeHashMap.newSet<Int>(400)) }
        .let { ConcurrentHashMap(it) }

    /**
     * @param reportAcked stores already counted orders trades so that bot not
     * to send them somewhere twice, e.g. to TG. These trades received later
     * comparing to [acked] and hold more data.
     */
    private val reportAcked: ConcurrentHashMap<SymbolEx, MutableSet<Int>> = strategyRepository.findAll()
        .map { listOf(it.legs.leg1.symbolEx, it.legs.leg2.symbolEx) }
        .flatten()
        .associateWith { Collections.synchronizedSet(MaxSizeHashMap.newSet<Int>(2000)) }
        .let { ConcurrentHashMap(it) }

    override val subscriptions: Array<Subscription<*>> = arrayOf(
        Subscription(ExExecutionEvent::class.java) { event -> this.onExecution(event) }
    )

    @Volatile
    private var lastExecution = 0L

    private val lastExecutionTimes: ConcurrentHashMap<String, Long> = ConcurrentHashMap()

    @PostConstruct
    fun init() {
        val strIds = strategyRepository.findAll().map { it.uuid }
        load()?.let { this.lastExecutionTimes.putAll(it.filterKeys { key -> strIds.contains(key) }) }
            .also {
                this.lastExecutionTimes.values.maxOrNull()?.let { this.lastExecution = it }
                // persist after load to save filtered out non-existent (deleted) strategies
                persist()
            }
    }

    private fun onExecution(event: ExExecutionEvent) {
        val snapshot = when (event.integrity) {
            Integrity.New -> false
            Integrity.Snapshot -> true
            else -> return
        }

        event.items.filterNot { failedExecutions(it) }
            .let { execs ->
                execs.groupBy { it.ordId }.forEach { (_, execs) ->
                    catching(this@ExExecutionHandler) {
                        handleExecForOrder(execs, snapshot, event.receivedAtNanos)
                    }
                }
            }
    }

    private fun failedExecutions(execution: ExExecution): Boolean {
        // some bulk updates
        return execution.ordId == "00000000-0000-0000-0000-000000000000"
                || execution.contracts == 0.0
                || execution.ordStatus == OrdStatus.Canceled
    }

    private fun handleExecForOrder(
        unfilteredExecutions: List<ExExecution>, snapshot: Boolean, receivedAt: Long
    ) {
        // executions only contain trades for same symbolEx
        val anyExecution = unfilteredExecutions.first()

        val (side, symbolEx) = anyExecution.let { Pair(it.side, it.symbolEx) }

        val (dbOrdLegId, botOrderId) = getDbOrdLegId(anyExecution) ?: return

        val strategy = strategyRepository.findByIdOrNull(dbOrdLegId.strategyId) ?: return

        // Leaving only new executions using [acked] map.
        val newExecutions = unfilteredExecutions.filter { acked[symbolEx]!!.add(it.id.hashCode()) }

        if (newExecutions.isEmpty()) { // all handled
            report(botOrderId, dbOrdLegId, strategy, unfilteredExecutions)
            return
        }

        val contracts = side.signed(newExecutions.sumOf { it.contracts })
        val usdSize = side.signed(exchangeRouter.contractsToUsd(symbolEx, contracts))

        // second leg adjusted in SecondLegSyncJob to react faster
        if (dbOrdLegId.legId == strategy.legs.leg1.id) {
            // saving price if open direction, otherwise ignoring
            val price = (dbOrdLegId.stage == Stage.Open).doIfTrueOrNull {
                unfilteredExecutions.first { it.price > 0.0 }.price
            }

            firstLegPositionRepository.adjust(strategy.uuid, usdSize, contracts, price)
        }

        val position = PositionUpdate(strategy.uuid, usdSize, dbOrdLegId.legId)
        eventPublisher.publishBlocking(PositionEvent(Integrity.New, listOf(position)))

        lastExecution = currentTimeMillis()
        lastExecutionTimes[strategy.uuid] = lastExecution

        log(dbOrdLegId, botOrderId, strategy, snapshot, newExecutions)
        report(botOrderId, dbOrdLegId, strategy, unfilteredExecutions)

        MetricsContext.put(TRADE, symbolEx, receivedAt)
    }

    private fun report(
        botOrderId: String,
        dbOrdLegId: DbOrdLegId,
        strategy: Strategy,
        unfilteredExecutions: List<ExExecution>
    ) {
        unfilteredExecutions
            .filter { it.feeAsset !== FeeAsset.MissingFee && reportAcked[it.symbolEx]!!.add(it.id.hashCode()) }
            .forEach { execution ->
                val contracts = execution.side.signed(execution.contracts)
                val usdSize = execution.side.signed(exchangeRouter.contractsToUsd(execution.symbolEx, contracts))

                val latency = currentTimeMillis() - execution.timeStamp.toEpochMilli()

                val filled = orderCache.fill(botOrderId, contracts.absoluteValue)

                val report = TradeReport(
                    execution.symbolEx, strategy.uuid,
                    execution.price, dbOrdLegId.stage, execution.side,
                    usdSize, contracts, latency / 1000.0, filled,
                    execution.cost(usdSize), execution.fee, execution.feeAsset
                )

                eventPublisher.publishAsync(TradeReportEvent(report))
            }
    }

    private fun log(
        dbOrdLegId: DbOrdLegId, botOrderId: String,
        strategy: Strategy, snapshot: Boolean,
        newExecutions: List<ExExecution>
    ) {
        newExecutions.forEach { execution ->
            val latency = currentTimeMillis() - execution.timeStamp.toEpochMilli()

            catching(this@ExExecutionHandler) {
                logMetrics(dbOrdLegId, botOrderId, strategy, execution, snapshot, latency)
            }
        }
    }

    private fun ExExecution.cost(usdSize: Double) = (usdSize / this.price).scaleTo(10)

    private fun logMetrics(
        dbOrdLegId: DbOrdLegId, botOrderId: String,
        strategy: Strategy, execution: ExExecution,
        snapshot: Boolean, latency: Long
    ) {
        (latency / 1000.0).let { sec ->
            log.info(
                "Order executed (snapshot = $snapshot, lat = $sec sec, " +
                        "bot quote = ${orderBookHolder.getLastQuote(execution.symbolEx, false)}): $execution"
            )
            if (sec > 1) {
                telegramSender.warnOnce("HIGH_LATENCY_TRADES", "${execution.symbolEx} trade latency is high: $sec sec")
            }
        }

        logMetric(
            LATENCY_TRADE, latency, execution.symbolEx.exchange, execution.symbolEx.symbol
        )

        if (dbOrdLegId.legId == strategy.legs.leg1.id) {
            val operation = if (dbOrdLegId.stage == Stage.Open) "opened" else "closed"

            val thresholds = meansHolder.thresholds(strategy)

            val dbOrder = orderCache.findByBotOrderId(
                execution.symbolEx.symbol, execution.side, botOrderId
            )

            if (dbOrder != null) {
                if (dbOrder.price.compareTo(execution.price) != 0) {
                    log.warn("${strategy.legs.leg1.symbolEx} price slippage: ${(dbOrder.price - execution.price)} ")
                }

                if (dbOrder.secondLegQuote != null) {
                    val quoteLat = currentTimeMillis() - dbOrder.secondLegQuote.timeStamp.toEpochMilli()
                    log.info("First leg $operation, $thresholds. Second leg prices when created last: ${dbOrder.secondLegQuote.last}, volumed: ${dbOrder.secondLegQuote.volumed} ${quoteLat / 1000.0} sec ago")
                } else {
                    log.info("First leg $operation, $thresholds. No quotes were captured")
                }
            }
        }
    }

    private fun getDbOrdLegId(execution: ExExecution): Pair<DbOrdLegId, String>? {
        val parsed = DbOrdLegId.fromText(execution.botOrderId)
        if (parsed != null) {
            return parsed to execution.botOrderId
        }

        // trying to find by ex order id in cache
        // for instance, sometimes binance return empty clientOrderId
        val dbOrdLegId = orderCache.findByExOrderId(execution.symbolEx.symbol, execution.side, execution.ordId)
        if (dbOrdLegId != null) {
            return dbOrdLegId.first to dbOrdLegId.second.botOrderId
        }

        log.warn("Unknown execution: $execution")
        if (telegramParams.unknownies) {
            telegramSender.warn("Unknown trade of ${execution.ordId} order: ${execution.symbolEx} ${execution.contracts}")
        }

        return null
    }

    fun lastExecutionTime() = lastExecution

    fun lastExecutionTime(strategyId: String) = lastExecutionTimes[strategyId] ?: 0L

    private fun load() = connection
        .sync()
        .get(LAST_EXECUTIONS_NAMESPACE)
        ?.toObj(object : TypeReference<Map<String, Long>>() {})

    private fun persist() {
        connection.async().set(LAST_EXECUTIONS_NAMESPACE, lastExecutionTimes.toJson())
    }

    private fun persistSync() {
        connection.sync().set(LAST_EXECUTIONS_NAMESPACE, lastExecutionTimes.toJson())
    }

    @EventListener
    fun persistOnShutdown(event: ApplicationShutdownEvent) {
        persistSync()
    }
}
